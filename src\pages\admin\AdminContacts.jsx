import React, { useState } from 'react';
import { MessageSquare, Eye, Edit, Trash2, Plus, Filter, Mail, Phone, Building } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { contactMessages, messageStatuses, messagePriorities } from '../../data/contactMessages';

const AdminContacts = () => {
  const [messages, setMessages] = useState(contactMessages);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewingMessage, setViewingMessage] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [newNote, setNewNote] = useState('');

  const handleView = (message) => {
    setViewingMessage(message);
    setIsModalOpen(true);
  };

  const handleStatusChange = (messageId, newStatus) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, status: newStatus, updated_date: new Date().toISOString() }
        : msg
    ));
    
    if (viewingMessage && viewingMessage.id === messageId) {
      setViewingMessage(prev => ({ ...prev, status: newStatus }));
    }
  };

  const handlePriorityChange = (messageId, newPriority) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, priority: newPriority, updated_date: new Date().toISOString() }
        : msg
    ));
    
    if (viewingMessage && viewingMessage.id === messageId) {
      setViewingMessage(prev => ({ ...prev, priority: newPriority }));
    }
  };

  const handleAddNote = () => {
    if (!newNote.trim() || !viewingMessage) return;
    
    const note = {
      id: Date.now(),
      note: newNote,
      created_by: 'Admin User',
      created_date: new Date().toISOString()
    };
    
    setMessages(prev => prev.map(msg => 
      msg.id === viewingMessage.id 
        ? { ...msg, notes: [...msg.notes, note], updated_date: new Date().toISOString() }
        : msg
    ));
    
    setViewingMessage(prev => ({ 
      ...prev, 
      notes: [...prev.notes, note] 
    }));
    
    setNewNote('');
  };

  const handleDelete = (message) => {
    if (window.confirm(`Are you sure you want to delete the message from "${message.name}"?`)) {
      setMessages(prev => prev.filter(msg => msg.id !== message.id));
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setViewingMessage(null);
    setNewNote('');
  };

  // Filter messages
  const filteredMessages = messages.filter(msg => {
    const statusMatch = selectedStatus === 'all' || msg.status === selectedStatus;
    const priorityMatch = selectedPriority === 'all' || msg.priority === selectedPriority;
    return statusMatch && priorityMatch;
  });

  const columns = [
    {
      key: 'name',
      label: 'NAME',
      sortable: true,
      render: (name, message) => (
        <div>
          <div className="font-bold">{name}</div>
          <div className="text-sm text-gray-600">{message.company}</div>
        </div>
      )
    },
    {
      key: 'email',
      label: 'CONTACT',
      render: (email, message) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="w-3 h-3" />
            <span>{email}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Phone className="w-3 h-3" />
            <span>{message.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'subject',
      label: 'SUBJECT',
      sortable: true,
      render: (subject) => (
        <span className="font-bold">{subject}</span>
      )
    },
    {
      key: 'status',
      label: 'STATUS',
      sortable: true,
      render: (status) => {
        const statusData = messageStatuses.find(s => s.value === status);
        return (
          <span className={`px-2 py-1 text-xs font-bold brutal-border ${statusData?.color} text-white`}>
            {statusData?.label || status.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'priority',
      label: 'PRIORITY',
      sortable: true,
      render: (priority) => {
        const priorityData = messagePriorities.find(p => p.value === priority);
        return (
          <span className={`px-2 py-1 text-xs font-bold brutal-border ${priorityData?.color} text-white`}>
            {priorityData?.label || priority.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'created_date',
      label: 'DATE',
      sortable: true,
      render: (date) => (
        <span className="text-sm">
          {new Date(date).toLocaleDateString('en-GB')}
        </span>
      )
    }
  ];

  const getStatusCounts = () => {
    const counts = { all: messages.length };
    messageStatuses.forEach(status => {
      counts[status.value] = messages.filter(msg => msg.status === status.value).length;
    });
    return counts;
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">CONTACT MESSAGES</h1>
            <p className="font-bold text-gray-600">
              Manage and respond to client inquiries and messages
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="bg-blue-500 text-white px-4 py-2 brutal-border brutal-shadow-small">
              <span className="font-bold">{messages.length} TOTAL MESSAGES</span>
            </div>
          </div>
        </div>
      </div>

      {/* Status Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => setSelectedStatus('all')}
            className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
              selectedStatus === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-black hover:bg-gray-50'
            }`}
          >
            ALL ({statusCounts.all})
          </button>
          
          {messageStatuses.map((status) => (
            <button
              key={status.value}
              onClick={() => setSelectedStatus(status.value)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedStatus === status.value
                  ? `${status.color} text-white`
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {status.label} ({statusCounts[status.value] || 0})
            </button>
          ))}
        </div>
        
        <div className="flex flex-wrap gap-2">
          <span className="font-bold text-sm">PRIORITY:</span>
          <button
            onClick={() => setSelectedPriority('all')}
            className={`px-3 py-1 text-sm brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
              selectedPriority === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-black hover:bg-gray-50'
            }`}
          >
            ALL
          </button>
          
          {messagePriorities.map((priority) => (
            <button
              key={priority.value}
              onClick={() => setSelectedPriority(priority.value)}
              className={`px-3 py-1 text-sm brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedPriority === priority.value
                  ? `${priority.color} text-white`
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {priority.label}
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredMessages}
        columns={columns}
        onView={handleView}
        onDelete={handleDelete}
        searchable={true}
        filterable={false}
      />

      {/* View Message Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title="MESSAGE DETAILS"
        size="large"
      >
        {viewingMessage && (
          <div className="space-y-6">
            {/* Message Header */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold text-sm text-gray-600 mb-1">CLIENT INFORMATION</h3>
                  <div className="bg-gray-50 p-4 brutal-border">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="font-bold">{viewingMessage.name}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="w-4 h-4" />
                        <span>{viewingMessage.email}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="w-4 h-4" />
                        <span>{viewingMessage.phone}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Building className="w-4 h-4" />
                        <span>{viewingMessage.company}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold text-sm text-gray-600 mb-1">MESSAGE STATUS</h3>
                  <div className="space-y-3">
                    <FormField
                      label="Status"
                      type="select"
                      value={viewingMessage.status}
                      onChange={(e) => handleStatusChange(viewingMessage.id, e.target.value)}
                      options={messageStatuses.map(s => ({ value: s.value, label: s.label }))}
                    />
                    
                    <FormField
                      label="Priority"
                      type="select"
                      value={viewingMessage.priority}
                      onChange={(e) => handlePriorityChange(viewingMessage.id, e.target.value)}
                      options={messagePriorities.map(p => ({ value: p.value, label: p.label }))}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Subject and Message */}
            <div>
              <h3 className="font-bold text-sm text-gray-600 mb-2">SUBJECT</h3>
              <div className="bg-blue-50 p-4 brutal-border">
                <span className="font-bold">{viewingMessage.subject}</span>
              </div>
            </div>

            <div>
              <h3 className="font-bold text-sm text-gray-600 mb-2">MESSAGE</h3>
              <div className="bg-gray-50 p-4 brutal-border">
                <p className="whitespace-pre-wrap">{viewingMessage.message}</p>
              </div>
            </div>

            {/* Notes Section */}
            <div>
              <h3 className="font-bold text-sm text-gray-600 mb-2">NOTES</h3>
              <div className="space-y-3">
                {viewingMessage.notes.map((note) => (
                  <div key={note.id} className="bg-yellow-50 p-3 brutal-border">
                    <p className="text-sm mb-2">{note.note}</p>
                    <div className="text-xs text-gray-600">
                      By {note.created_by} on {new Date(note.created_date).toLocaleString()}
                    </div>
                  </div>
                ))}
                
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    placeholder="Add a note..."
                    className="flex-1 px-3 py-2 brutal-border brutal-shadow-small focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                  />
                  <ActionButton
                    onClick={handleAddNote}
                    variant="primary"
                    size="small"
                    icon={Plus}
                  >
                    ADD NOTE
                  </ActionButton>
                </div>
              </div>
            </div>

            {/* Message Info */}
            <div className="text-sm text-gray-600 bg-gray-50 p-3 brutal-border">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-bold">Received:</span> {new Date(viewingMessage.created_date).toLocaleString()}
                </div>
                <div>
                  <span className="font-bold">Last Updated:</span> {new Date(viewingMessage.updated_date).toLocaleString()}
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AdminContacts;
