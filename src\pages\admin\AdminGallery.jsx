import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Upload, Filter } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { galleryImages, galleryCategories } from '../../data/galleryImages';

const AdminGallery = () => {
  const [images, setImages] = useState(galleryImages);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingImage, setEditingImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [formData, setFormData] = useState({
    title: '',
    alt: '',
    category: '',
    size: 'medium',
    description: '',
    src: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingImage) {
      // Update existing image
      setImages(prev => prev.map(img => 
        img.id === editingImage.id 
          ? { ...img, ...formData }
          : img
      ));
    } else {
      // Add new image
      const newImage = {
        id: Math.max(...images.map(img => img.id)) + 1,
        ...formData,
        src: formData.src || 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop'
      };
      setImages(prev => [...prev, newImage]);
    }
    
    handleCloseModal();
  };

  const handleEdit = (image) => {
    setEditingImage(image);
    setFormData({
      title: image.title,
      alt: image.alt,
      category: image.category,
      size: image.size,
      description: image.description,
      src: image.src
    });
    setIsModalOpen(true);
  };

  const handleDelete = (image) => {
    if (window.confirm(`Are you sure you want to delete "${image.title}"?`)) {
      setImages(prev => prev.filter(img => img.id !== image.id));
    }
  };

  const handleView = (image) => {
    window.open(image.src, '_blank');
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingImage(null);
    setFormData({
      title: '',
      alt: '',
      category: '',
      size: 'medium',
      description: '',
      src: ''
    });
  };

  const handleAddNew = () => {
    setEditingImage(null);
    setFormData({
      title: '',
      alt: '',
      category: '',
      size: 'medium',
      description: '',
      src: ''
    });
    setIsModalOpen(true);
  };

  // Filter images by category
  const filteredImages = selectedCategory === 'all' 
    ? images 
    : images.filter(img => img.category === selectedCategory);

  const columns = [
    {
      key: 'src',
      label: 'IMAGE',
      render: (src) => (
        <img 
          src={src} 
          alt="Gallery item" 
          className="w-16 h-16 object-cover brutal-border"
        />
      )
    },
    {
      key: 'title',
      label: 'TITLE',
      sortable: true
    },
    {
      key: 'category',
      label: 'CATEGORY',
      sortable: true,
      render: (category) => {
        const categoryData = galleryCategories.find(cat => cat.id === category);
        return (
          <span className={`px-2 py-1 text-xs font-bold brutal-border ${categoryData?.color || 'bg-gray-500'} text-white`}>
            {categoryData?.name || category.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'size',
      label: 'SIZE',
      sortable: true,
      render: (size) => (
        <span className="font-bold uppercase">{size}</span>
      )
    },
    {
      key: 'description',
      label: 'DESCRIPTION',
      render: (description) => (
        <span className="text-sm">{description.length > 50 ? `${description.substring(0, 50)}...` : description}</span>
      )
    }
  ];

  const sizeOptions = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
    { value: 'wide', label: 'Wide' },
    { value: 'tall', label: 'Tall' },
    { value: 'square', label: 'Square' }
  ];

  const categoryOptions = galleryCategories
    .filter(cat => cat.id !== 'all')
    .map(cat => ({ value: cat.id, label: cat.name }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">GALLERY MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Manage your gallery images, categories, and metadata
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW IMAGE
          </ActionButton>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2">
          {galleryCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedCategory === category.id
                  ? `${category.color} text-white`
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredImages}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={false}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingImage ? 'EDIT GALLERY IMAGE' : 'ADD NEW GALLERY IMAGE'}
        size="large"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Image Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter image title"
              required
            />
            
            <FormField
              label="Alt Text"
              name="alt"
              value={formData.alt}
              onChange={handleInputChange}
              placeholder="Enter alt text for accessibility"
              required
            />
            
            <FormField
              label="Category"
              type="select"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              options={categoryOptions}
              required
            />
            
            <FormField
              label="Size"
              type="select"
              name="size"
              value={formData.size}
              onChange={handleInputChange}
              options={sizeOptions}
              required
            />
          </div>
          
          <FormField
            label="Image URL"
            name="src"
            value={formData.src}
            onChange={handleInputChange}
            placeholder="Enter image URL or upload file"
            required={!editingImage}
          />
          
          <FormField
            label="Description"
            type="textarea"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter image description"
            rows={3}
            required
          />
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingImage ? Edit : Plus}
            >
              {editingImage ? 'UPDATE IMAGE' : 'ADD IMAGE'}
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminGallery;
