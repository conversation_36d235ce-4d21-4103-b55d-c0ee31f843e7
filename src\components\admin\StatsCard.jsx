import React from 'react';

const StatsCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color = 'blue', 
  trend, 
  trendValue,
  className = '' 
}) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-white',
    yellow: 'bg-yellow-400 text-black',
    green: 'bg-green-400 text-black',
    red: 'bg-red-500 text-white',
    pink: 'bg-pink-500 text-white',
    purple: 'bg-purple-500 text-white',
    gray: 'bg-gray-500 text-white'
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600'
  };

  return (
    <div className={`bg-white brutal-border brutal-shadow p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 brutal-border brutal-shadow-small ${colorClasses[color]}`}>
          {Icon && <Icon className="w-6 h-6" />}
        </div>
        
        {trend && trendValue && (
          <div className={`text-sm font-bold ${trendColors[trend]}`}>
            {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'} {trendValue}
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <h3 className="brutal-text text-2xl">{value}</h3>
        <p className="font-bold text-gray-600 text-sm uppercase">{title}</p>
      </div>
    </div>
  );
};

export default StatsCard;
