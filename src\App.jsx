import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/layout/Layout';

// Import pages
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import Process from './pages/Process';
import Blog from './pages/Blog';
import Resources from './pages/Resources';
import Testimonials from './pages/Testimonials';
import CaseStudies from './pages/CaseStudies';
import Tools from './pages/Tools';
import FAQ from './pages/FAQ';
import Contact from './pages/Contact';
import Gallery from './pages/Gallery';
import PrivacyPolicy from './pages/PrivacyPolicy';
import Terms from './pages/Terms';

// Import admin pages
import AdminLayout from './components/admin/AdminLayout';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminGallery from './pages/admin/AdminGallery';
import AdminContacts from './pages/admin/AdminContacts';
import AdminCaseStudies from './pages/admin/AdminCaseStudies';
import AdminResources from './pages/admin/AdminResources';
import AdminBlog from './pages/admin/AdminBlog';
import AdminTestimonials from './pages/admin/AdminTestimonials';

const ServiceDetail = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="bg-purple-500 text-white p-8 brutal-border brutal-shadow text-center">
      <h1 className="brutal-text text-4xl mb-4">SERVICE DETAIL</h1>
      <p className="font-bold">Coming soon! This page will show service details.</p>
    </div>
  </div>
);

const BlogPost = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="bg-indigo-500 text-white p-8 brutal-border brutal-shadow text-center">
      <h1 className="brutal-text text-4xl mb-4">BLOG POST</h1>
      <p className="font-bold">Coming soon! This page will show individual blog posts.</p>
    </div>
  </div>
);

const CaseStudyDetail = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="bg-red-500 text-white p-8 brutal-border brutal-shadow text-center">
      <h1 className="brutal-text text-4xl mb-4">CASE STUDY DETAIL</h1>
      <p className="font-bold">Coming soon! This page will show detailed case studies.</p>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={
          <Layout currentPageName="Home">
            <Home />
          </Layout>
        } />
        
        <Route path="/about" element={
          <Layout currentPageName="About">
            <About />
          </Layout>
        } />
        
        <Route path="/services" element={
          <Layout currentPageName="Services">
            <Services />
          </Layout>
        } />
        
        <Route path="/process" element={
          <Layout currentPageName="Process">
            <Process />
          </Layout>
        } />
        
        <Route path="/case-studies" element={
          <Layout currentPageName="CaseStudies">
            <CaseStudies />
          </Layout>
        } />
        
        <Route path="/testimonials" element={
          <Layout currentPageName="Testimonials">
            <Testimonials />
          </Layout>
        } />
        
        <Route path="/resources" element={
          <Layout currentPageName="Resources">
            <Resources />
          </Layout>
        } />
        
        <Route path="/tools" element={
          <Layout currentPageName="Tools">
            <Tools />
          </Layout>
        } />
        
        <Route path="/blog" element={
          <Layout currentPageName="Blog">
            <Blog />
          </Layout>
        } />
        
        <Route path="/faq" element={
          <Layout currentPageName="FAQ">
            <FAQ />
          </Layout>
        } />
        
        <Route path="/contact" element={
          <Layout currentPageName="Contact">
            <Contact />
          </Layout>
        } />

        <Route path="/gallery" element={
          <Layout currentPageName="Gallery">
            <Gallery />
          </Layout>
        } />

        <Route path="/privacy-policy" element={
          <Layout currentPageName="PrivacyPolicy">
            <PrivacyPolicy />
          </Layout>
        } />
        
        <Route path="/terms" element={
          <Layout currentPageName="Terms">
            <Terms />
          </Layout>
        } />
        
        <Route path="/service-detail" element={
          <Layout currentPageName="ServiceDetail">
            <ServiceDetail />
          </Layout>
        } />
        
        <Route path="/blog-post/:slug" element={
          <Layout currentPageName="BlogPost">
            <BlogPost />
          </Layout>
        } />
        
        <Route path="/case-study-detail/:id" element={
          <Layout currentPageName="CaseStudyDetail">
            <CaseStudyDetail />
          </Layout>
        } />

        {/* Admin Routes */}
        <Route path="/admin" element={
          <AdminLayout>
            <AdminDashboard />
          </AdminLayout>
        } />

        <Route path="/admin/gallery" element={
          <AdminLayout>
            <AdminGallery />
          </AdminLayout>
        } />

        <Route path="/admin/contacts" element={
          <AdminLayout>
            <AdminContacts />
          </AdminLayout>
        } />

        <Route path="/admin/case-studies" element={
          <AdminLayout>
            <AdminCaseStudies />
          </AdminLayout>
        } />

        <Route path="/admin/resources" element={
          <AdminLayout>
            <AdminResources />
          </AdminLayout>
        } />

        <Route path="/admin/blog" element={
          <AdminLayout>
            <AdminBlog />
          </AdminLayout>
        } />

        <Route path="/admin/testimonials" element={
          <AdminLayout>
            <AdminTestimonials />
          </AdminLayout>
        } />

        {/* 404 Page */}
        <Route path="*" element={
          <Layout currentPageName="NotFound">
            <div className="min-h-screen bg-white flex items-center justify-center">
              <div className="bg-red-500 text-white p-8 brutal-border brutal-shadow text-center">
                <h1 className="brutal-text text-4xl mb-4">404 - PAGE NOT FOUND</h1>
                <p className="font-bold">The page you're looking for doesn't exist.</p>
              </div>
            </div>
          </Layout>
        } />
      </Routes>
    </Router>
  );
}

export default App;
