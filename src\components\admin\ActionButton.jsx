import React from 'react';

const ActionButton = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  onClick, 
  disabled = false, 
  className = '', 
  icon: Icon,
  loading = false,
  type = 'button',
  ...props 
}) => {
  const baseClasses = 'brutal-border brutal-shadow brutal-text transition-all duration-150 hover:translate-x-2 hover:translate-y-2 hover:shadow-none inline-flex items-center justify-center gap-2 font-bold';
  
  const variants = {
    primary: 'bg-blue-500 text-white hover:bg-blue-600',
    secondary: 'bg-yellow-400 text-black hover:bg-yellow-500',
    success: 'bg-green-400 text-black hover:bg-green-500',
    danger: 'bg-red-500 text-white hover:bg-red-600',
    warning: 'bg-orange-500 text-white hover:bg-orange-600',
    info: 'bg-cyan-500 text-white hover:bg-cyan-600',
    dark: 'bg-black text-white hover:bg-gray-800',
    outline: 'bg-white text-black border-black hover:bg-gray-50'
  };

  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-sm',
    large: 'px-6 py-4 text-lg'
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${
    disabled || loading ? 'opacity-50 cursor-not-allowed hover:translate-x-0 hover:translate-y-0 hover:shadow-brutal-small' : ''
  } ${className}`;

  return (
    <button 
      type={type}
      onClick={onClick} 
      disabled={disabled || loading} 
      className={classes} 
      {...props}
    >
      {loading ? (
        <>
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          Loading...
        </>
      ) : (
        <>
          {Icon && <Icon className="w-4 h-4" />}
          {children}
        </>
      )}
    </button>
  );
};

export default ActionButton;
